[project]
name = "compliance_assistant"
version = "0.1.0"
description = "compliance-assistant using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "bandit>=1.8.3",
    "boto3>=1.37.6",
    "crewai[tools]>=0.105.0,<1.0.0"
]

[project.scripts]
compliance_assistant = "compliance_assistant.main:run"
run_crew = "compliance_assistant.main:run"
train = "compliance_assistant.main:train"
replay = "compliance_assistant.main:replay"
test = "compliance_assistant.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
